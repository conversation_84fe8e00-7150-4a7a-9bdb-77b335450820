#!/usr/bin/env python3
"""
Simple launcher for the Anime Release Finder GUI
"""

import sys
import os

# Add current directory to path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Main launcher function"""
    print("🎌 Anime Release Finder - GUI Launcher")
    print("=" * 40)
    
    try:
        # Try to import and run the GUI
        from anime_gui import main as gui_main
        print("✅ Starting GUI...")
        gui_main()
        
    except ImportError as e:
        print(f"❌ Error importing required modules: {e}")
        print("\n📦 Required packages:")
        print("   pip install PyQt6 requests")
        print("\n💡 Alternative: Run command-line version:")
        print("   python anime_release_finder.py")
        input("\nPress Enter to exit...")
        
    except Exception as e:
        print(f"❌ Error running application: {e}")
        print("\n💡 Try running the command-line version:")
        print("   python anime_release_finder.py")
        input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
