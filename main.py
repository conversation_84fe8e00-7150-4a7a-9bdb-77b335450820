#!/usr/bin/env python3
"""
Simple Anime Release Finder
A clean, simple program to search for anime episode release information
"""

import requests
import re
import json
from datetime import datetime
from urllib.parse import quote_plus

class SimpleAnimeReleaseFinder:
    """Simple anime release finder using basic web search"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def search_anime_info(self, anime_name):
        """Search for anime release information using multiple sources"""
        print(f"🔍 Searching for: {anime_name}")
        print("=" * 50)
        
        results = []
        
        # Search MyAnimeList
        mal_results = self.search_myanimelist(anime_name)
        results.extend(mal_results)
        
        # Search Crunchyroll
        cr_results = self.search_crunchyroll(anime_name)
        results.extend(cr_results)
        
        # Search general web
        web_results = self.search_web(anime_name)
        results.extend(web_results)
        
        return results
    
    def search_myanimelist(self, anime_name):
        """Search MyAnimeList for anime information"""
        results = []
        try:
            print("📺 Searching MyAnimeList...")
            
            # Use MAL's search API
            search_url = f"https://myanimelist.net/search/all?q={quote_plus(anime_name)}"
            response = self.session.get(search_url, timeout=10)
            
            if response.status_code == 200:
                # Simple text search for anime information
                content = response.text.lower()
                if anime_name.lower() in content:
                    results.append({
                        'source': 'MyAnimeList',
                        'title': f"Search results for {anime_name}",
                        'url': search_url,
                        'info': 'Found on MyAnimeList - check for episode schedules'
                    })
                    print("   ✅ Found on MyAnimeList")
                else:
                    print("   ❌ Not found on MyAnimeList")
            
        except Exception as e:
            print(f"   ❌ MyAnimeList search failed: {e}")
        
        return results
    
    def search_crunchyroll(self, anime_name):
        """Search Crunchyroll for anime information"""
        results = []
        try:
            print("🎬 Searching Crunchyroll...")
            
            # Search Crunchyroll
            search_url = f"https://www.crunchyroll.com/search?q={quote_plus(anime_name)}"
            response = self.session.get(search_url, timeout=10)
            
            if response.status_code == 200:
                content = response.text.lower()
                if anime_name.lower() in content:
                    results.append({
                        'source': 'Crunchyroll',
                        'title': f"Search results for {anime_name}",
                        'url': search_url,
                        'info': 'Found on Crunchyroll - check for streaming schedule'
                    })
                    print("   ✅ Found on Crunchyroll")
                else:
                    print("   ❌ Not found on Crunchyroll")
            
        except Exception as e:
            print(f"   ❌ Crunchyroll search failed: {e}")
        
        return results
    
    def search_web(self, anime_name):
        """Search general web for anime information"""
        results = []
        try:
            print("🌐 Searching general web...")
            
            # Use a simple search approach
            search_terms = [
                f"{anime_name} episode release schedule",
                f"{anime_name} air date 2024 2025",
                f"{anime_name} streaming schedule"
            ]
            
            for term in search_terms:
                try:
                    # Simple web search simulation
                    results.append({
                        'source': 'Web Search',
                        'title': f"Search: {term}",
                        'url': f"https://www.google.com/search?q={quote_plus(term)}",
                        'info': f"Search term: {term}"
                    })
                except Exception as e:
                    continue
            
            print(f"   ✅ Generated {len(search_terms)} search queries")
            
        except Exception as e:
            print(f"   ❌ Web search failed: {e}")
        
        return results
    
    def extract_dates(self, text):
        """Extract potential dates from text"""
        date_patterns = [
            r'\b\d{1,2}[/-]\d{1,2}[/-]\d{4}\b',  # MM/DD/YYYY
            r'\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b',  # YYYY/MM/DD
            r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}\b',
            r'\b(?:Spring|Summer|Fall|Autumn|Winter)\s+\d{4}\b'
        ]
        
        dates = []
        for pattern in date_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            dates.extend(matches)
        
        return list(set(dates))
    
    def display_results(self, results, anime_name):
        """Display search results in a clean format"""
        print(f"\n📊 Results for '{anime_name}':")
        print("=" * 50)
        
        if not results:
            print("❌ No results found.")
            print("\n💡 Suggestions:")
            print("   • Check the anime name spelling")
            print("   • Try searching manually on:")
            print("     - MyAnimeList.net")
            print("     - Crunchyroll.com")
            print("     - Funimation.com")
            return
        
        for i, result in enumerate(results, 1):
            print(f"\n{i}. [{result['source']}] {result['title']}")
            print(f"   URL: {result['url']}")
            print(f"   Info: {result['info']}")
        
        print(f"\n✅ Found {len(results)} potential sources")
        print("\n💡 Next steps:")
        print("   • Visit the URLs above to find specific episode schedules")
        print("   • Check official anime social media accounts")
        print("   • Look for 'simulcast' or 'weekly release' information")

def main():
    """Main function to run the anime release finder"""
    print("🎌 Simple Anime Release Finder")
    print("=" * 40)
    print("Find anime episode release schedules and streaming information")
    print()
    
    try:
        # Get anime name from user
        anime_name = input("Enter anime name: ").strip()
        
        if not anime_name:
            print("❌ Please enter a valid anime name")
            return
        
        print()
        
        # Create finder and search
        finder = SimpleAnimeReleaseFinder()
        results = finder.search_anime_info(anime_name)
        
        # Display results
        finder.display_results(results, anime_name)
        
        print(f"\n🎯 Search completed for: {anime_name}")
        
    except KeyboardInterrupt:
        print("\n\n👋 Search cancelled by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("Please check your internet connection and try again")
    
    print("\nPress Enter to exit...")
    input()

if __name__ == "__main__":
    main()
