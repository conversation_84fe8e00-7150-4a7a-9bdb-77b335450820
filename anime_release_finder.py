#!/usr/bin/env python3
"""
Anime Release Finder
Searches the web (including Tor) for anime release dates and English dub information
"""

import requests
import json
import time
import re
from datetime import datetime
from urllib.parse import quote_plus
import socks
import socket
from duckduckgo_search import DDGS
from bs4 import BeautifulSoup

class AnimeReleaseFinder:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def search_regular_web(self, anime_name, dubbed=False):
        """Search regular web for anime episode release schedules"""
        results = []
        
        # DuckDuckGo search with anime-specific terms
        try:
            ddgs = DDGS()
            # Focus on episode releases and streaming schedules
            queries = [
                f"{anime_name} episode release schedule 2024 2025",
                f"{anime_name} next episode air date streaming",
                f"{anime_name} weekly release schedule crunchyroll funimation",
                f"{anime_name} episode calendar when does air"
            ]
            
            for query in queries:
                print(f"🔍 Searching: {query}")
                ddg_results = ddgs.text(query, max_results=5)
                
                for result in ddg_results:
                    # Filter for anime-relevant results
                    if self._is_anime_relevant(result, anime_name):
                        results.append({
                            'source': 'DuckDuckGo',
                            'title': result['title'],
                            'url': result['href'],
                            'snippet': result['body']
                        })
        except Exception as e:
            print(f"DuckDuckGo search failed: {e}")
        
        # Search anime-specific sites with focused queries
        anime_sites = [
            "myanimelist.net",
            "anilist.co", 
            "crunchyroll.com",
            "funimation.com",
            "animenewsnetwork.com",
            "livechart.me"
        ]
        
        for site in anime_sites:
            try:
                query = f"site:{site} {anime_name} episode release schedule air date"
                ddg_results = ddgs.text(query, max_results=3)
                
                for result in ddg_results:
                    if self._is_anime_relevant(result, anime_name):
                        results.append({
                            'source': f'Anime Site ({site})',
                            'title': result['title'],
                            'url': result['href'],
                            'snippet': result['body']
                        })
            except Exception as e:
                print(f"Search on {site} failed: {e}")
        
        return results
    
    def _is_anime_relevant(self, result, anime_name):
        """Check if search result is relevant to anime episode releases"""
        title_lower = result['title'].lower()
        snippet_lower = result['body'].lower()
        anime_lower = anime_name.lower()
        
        # Must contain anime name
        if anime_lower not in title_lower and anime_lower not in snippet_lower:
            return False
        
        # Must contain anime/episode/release related terms
        anime_keywords = [
            'episode', 'anime', 'release', 'air', 'stream', 'broadcast',
            'season', 'crunchyroll', 'funimation', 'netflix', 'hulu',
            'weekly', 'schedule', 'premiere', 'finale', 'dubbed', 'sub'
        ]
        
        has_anime_keywords = any(keyword in title_lower or keyword in snippet_lower 
                                for keyword in anime_keywords)
        
        # Exclude irrelevant content
        exclude_keywords = [
            'manga', 'novel', 'book', 'game', 'toy', 'figure', 'merchandise',
            'cosplay', 'convention', 'news article', 'review only', 'trailer only'
        ]
        
        has_exclude_keywords = any(keyword in title_lower or keyword in snippet_lower 
                                  for keyword in exclude_keywords)
        
        return has_anime_keywords and not has_exclude_keywords

    def search_tor_network(self, anime_name, dubbed=False):
        """Search Tor network for anime information"""
        results = []
        
        try:
            # Configure Tor proxy
            socks.set_default_proxy(socks.SOCKS5, "127.0.0.1", 9050)
            socket.socket = socks.socksocket
            
            tor_session = requests.Session()
            tor_session.proxies = {
                'http': 'socks5://127.0.0.1:9050',
                'https': 'socks5://127.0.0.1:9050'
            }
            tor_session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            # Tor search engines and sites
            tor_search_engines = [
                "http://3g2upl4pq6kufc4m.onion",  # DuckDuckGo Tor
                "http://facebookwkhpilnemxj7asaniu7vnjjbiltxjqhye3mhbshg7kx5tfyd.onion"  # Facebook Tor
            ]
            
            dub_term = "english dub" if dubbed else ""
            query = f"{anime_name} release date {dub_term}"
            
            print(f"🧅 Searching Tor network for: {query}")
            
            for search_engine in tor_search_engines:
                try:
                    response = tor_session.get(f"{search_engine}/search?q={quote_plus(query)}", timeout=30)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        # Parse search results (simplified)
                        for link in soup.find_all('a', href=True)[:5]:
                            if 'anime' in link.text.lower() or anime_name.lower() in link.text.lower():
                                results.append({
                                    'source': 'Tor Network',
                                    'title': link.text.strip(),
                                    'url': link['href'],
                                    'snippet': 'Found via Tor network search'
                                })
                except Exception as e:
                    print(f"Tor search engine {search_engine} failed: {e}")
                    
        except Exception as e:
            print(f"Tor network search failed: {e}")
            print("Make sure Tor is running on port 9050")
        
        return results
    
    def search_japanese_sites(self, anime_name, dubbed=False):
        """Search Japanese anime sites"""
        results = []
        
        japanese_sites = [
            "anime-japan.jp",
            "natalie.mu/comic",
            "oricon.co.jp",
            "animatetimes.com"
        ]
        
        try:
            for site in japanese_sites:
                dub_term = "英語吹き替え" if dubbed else ""  # Japanese for "English dub"
                query = f"site:{site} {anime_name} {dub_term} 放送日"  # "broadcast date" in Japanese
                
                ddgs = DDGS()
                jp_results = ddgs.text(query, max_results=3)
                
                for result in jp_results:
                    results.append({
                        'source': f'Japanese Site ({site})',
                        'title': result['title'],
                        'url': result['href'],
                        'snippet': result['body']
                    })
                    
        except Exception as e:
            print(f"Japanese sites search failed: {e}")
        
        return results
    
    def extract_dates(self, text):
        """Extract potential release dates and times from text"""
        date_patterns = [
            r'\b\d{1,2}[/-]\d{1,2}[/-]\d{4}\b',  # MM/DD/YYYY or MM-DD-YYYY
            r'\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b',  # YYYY/MM/DD or YYYY-MM-DD
            r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}\b',
            r'\b\d{1,2}\s+(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}\b',
            r'\b(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)\s+at\s+\d{1,2}:\d{2}\s*(?:AM|PM|JST|EST|PST)?\b',
            r'\b(?:Spring|Summer|Fall|Autumn|Winter)\s+\d{4}\b',
            r'\b\d{1,2}:\d{2}\s*(?:AM|PM|JST|EST|PST)\b',  # Time patterns
            r'\b(?:weekly|every)\s+(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)\b',
            r'\b\d{4}\b'  # Just year
        ]
        
        dates = []
        for pattern in date_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            dates.extend(matches)
        
        return list(set(dates))  # Remove duplicates
    
    def analyze_results(self, all_results, anime_name, dubbed):
        """Analyze search results and extract relevant information"""
        print(f"\n📊 Analysis for '{anime_name}' ({'English Dub' if dubbed else 'Original/Sub'}):")
        print("=" * 60)
        
        all_dates = []
        relevant_results = []
        
        for result in all_results:
            # Check if result is relevant
            title_lower = result['title'].lower()
            snippet_lower = result['snippet'].lower()
            anime_lower = anime_name.lower()
            
            relevance_score = 0
            if anime_lower in title_lower:
                relevance_score += 3
            if anime_lower in snippet_lower:
                relevance_score += 2
            if dubbed and any(term in snippet_lower for term in ['dub', 'english', 'dubbed']):
                relevance_score += 2
            if any(term in snippet_lower for term in ['release', 'premiere', 'air', 'broadcast']):
                relevance_score += 1
            
            if relevance_score >= 2:
                relevant_results.append((result, relevance_score))
                
                # Extract dates
                dates = self.extract_dates(result['title'] + ' ' + result['snippet'])
                all_dates.extend(dates)
        
        # Sort by relevance
        relevant_results.sort(key=lambda x: x[1], reverse=True)
        
        # Display results
        print(f"Found {len(relevant_results)} relevant results:")
        print()
        
        for i, (result, score) in enumerate(relevant_results[:10], 1):
            print(f"{i}. [{result['source']}] {result['title']}")
            print(f"   URL: {result['url']}")
            print(f"   Snippet: {result['snippet'][:200]}...")
            print(f"   Relevance Score: {score}")
            print()
        
        # Display extracted dates
        if all_dates:
            print("🗓️  Potential Release Dates Found:")
            for date in sorted(set(all_dates)):
                print(f"   • {date}")
        else:
            print("❌ No specific dates found in search results")
        
        return relevant_results, all_dates

    def search_social_media(self, anime_name, dubbed=False):
        """Search social media and news sites for anime dub announcements"""
        results = []
        
        # Social media and news sites
        social_sites = [
            "twitter.com",
            "reddit.com/r/anime",
            "reddit.com/r/Animedubs", 
            "facebook.com",
            "instagram.com",
            "youtube.com",
            "tiktok.com"
        ]
        
        news_sites = [
            "animenewsnetwork.com",
            "crunchyroll.com/news",
            "funimation.com/blog",
            "polygon.com",
            "ign.com",
            "kotaku.com",
            "variety.com"
        ]
        
        try:
            ddgs = DDGS()
            
            # Search social media
            for site in social_sites:
                dub_terms = ["english dub", "dub announcement", "english voice cast", "dub release"]
                for dub_term in dub_terms:
                    query = f"site:{site} {anime_name} {dub_term}"
                    try:
                        social_results = ddgs.text(query, max_results=5)
                        for result in social_results:
                            results.append({
                                'source': f'Social Media ({site})',
                                'title': result['title'],
                                'url': result['href'],
                                'snippet': result['body']
                            })
                    except Exception as e:
                        print(f"Social search on {site} failed: {e}")
            
            # Search news sites
            for site in news_sites:
                query = f"site:{site} {anime_name} english dub"
                try:
                    news_results = ddgs.text(query, max_results=3)
                    for result in news_results:
                        results.append({
                            'source': f'News ({site})',
                            'title': result['title'],
                            'url': result['href'],
                            'snippet': result['body']
                        })
                except Exception as e:
                    print(f"News search on {site} failed: {e}")
                    
        except Exception as e:
            print(f"Social media search failed: {e}")
        
        return results

    def search_japanese_creators(self, anime_name, dubbed=False):
        """Search Japanese creator sites and official sources"""
        results = []
        
        # Japanese studio and creator sites
        japanese_sources = [
            "toei-anim.co.jp",
            "madhouse.co.jp", 
            "studioghibli.co.jp",
            "mappa.co.jp",
            "wit-studio.com",
            "bones.co.jp",
            "pierrot.co.jp",
            "sunrise-inc.co.jp",
            "kadokawa.co.jp",
            "shueisha.co.jp",
            "kodansha.co.jp"
        ]
        
        # Japanese social media and platforms
        japanese_social = [
            "twitter.com",  # Many Japanese creators use Twitter
            "nicovideo.jp",
            "pixiv.net",
            "note.com"
        ]
        
        try:
            ddgs = DDGS()
            
            # Search official studio sites
            for site in japanese_sources:
                # Search in both English and Japanese
                queries = [
                    f"site:{site} {anime_name} english dub",
                    f"site:{site} {anime_name} 英語吹き替え",
                    f"site:{site} {anime_name} 海外版"  # "overseas version"
                ]
                
                for query in queries:
                    try:
                        jp_results = ddgs.text(query, max_results=2)
                        for result in jp_results:
                            results.append({
                                'source': f'Japanese Studio ({site})',
                                'title': result['title'],
                                'url': result['href'],
                                'snippet': result['body']
                            })
                    except Exception as e:
                        continue
            
            # Search Japanese social media
            for site in japanese_social:
                # Focus on English dub announcements
                dub_queries = [
                    f"site:{site} {anime_name} english dub announcement",
                    f"site:{site} {anime_name} 英語版",
                    f"site:{site} {anime_name} English voice"
                ]
                
                for query in dub_queries:
                    try:
                        social_results = ddgs.text(query, max_results=3)
                        for result in social_results:
                            results.append({
                                'source': f'Japanese Social ({site})',
                                'title': result['title'],
                                'url': result['href'],
                                'snippet': result['body']
                            })
                    except Exception as e:
                        continue
                        
        except Exception as e:
            print(f"Japanese creator search failed: {e}")
        
        return results

    def search_streaming_platforms(self, anime_name, dubbed=False):
        """Search streaming platforms for anime schedules"""
        results = []
        
        streaming_platforms = [
            ("crunchyroll.com", "Crunchyroll"),
            ("funimation.com", "Funimation"), 
            ("netflix.com", "Netflix"),
            ("hulu.com", "Hulu"),
            ("disney.com", "Disney+"),
            ("amazon.com", "Prime Video"),
            ("hidive.com", "HIDIVE")
        ]
        
        try:
            ddgs = DDGS()
            for site, platform_name in streaming_platforms:
                dub_term = "english dub" if dubbed else ""
                query = f"site:{site} {anime_name} {dub_term} episode schedule release"
                
                print(f"🔍 Searching {platform_name} for: {anime_name}")
                platform_results = ddgs.text(query, max_results=3)
                
                for result in platform_results:
                    if self._is_anime_relevant(result, anime_name):
                        results.append({
                            'source': f'{platform_name} Streaming',
                            'title': result['title'],
                            'url': result['href'],
                            'snippet': result['body']
                        })
                        
        except Exception as e:
            print(f"Streaming platforms search failed: {e}")
        
        return results

    def search_anime_schedule_sites(self, anime_name, dubbed=False):
        """Search anime schedule and database sites"""
        results = []
        
        schedule_sites = [
            ("livechart.me", "LiveChart"),
            ("myanimelist.net", "MyAnimeList"),
            ("anilist.co", "AniList"),
            ("animenewsnetwork.com", "Anime News Network"),
            ("anichart.net", "AniChart")
        ]
        
        try:
            ddgs = DDGS()
            for site, site_name in schedule_sites:
                query = f"site:{site} {anime_name} episode release air date schedule"
                
                print(f"🔍 Searching {site_name} for: {anime_name}")
                site_results = ddgs.text(query, max_results=4)
                
                for result in site_results:
                    if self._is_anime_relevant(result, anime_name):
                        results.append({
                            'source': f'{site_name} Database',
                            'title': result['title'],
                            'url': result['href'],
                            'snippet': result['body']
                        })
                        
        except Exception as e:
            print(f"Anime schedule sites search failed: {e}")
        
        return results

    def search_tor_anime_communities(self, anime_name, dubbed=False):
        """Search Tor network anime communities and forums"""
        results = []
        
        try:
            # Configure Tor proxy
            socks.set_default_proxy(socks.SOCKS5, "127.0.0.1", 9050)
            socket.socket = socks.socksocket
            
            tor_session = requests.Session()
            tor_session.proxies = {
                'http': 'socks5://127.0.0.1:9050',
                'https': 'socks5://127.0.0.1:9050'
            }
            tor_session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            # Tor anime communities and forums
            tor_anime_sites = [
                "http://3g2upl4pq6kufc4m.onion",  # DuckDuckGo Tor
                "http://facebookwkhpilnemxj7asaniu7vnjjbiltxjqhye3mhbshg7kx5tfyd.onion"  # Facebook Tor
            ]
            
            dub_queries = [
                f"{anime_name} english dub release",
                f"{anime_name} dub announcement",
                f"{anime_name} english voice cast"
            ]
            
            print(f"🧅 Searching Tor anime communities...")
            
            for search_engine in tor_anime_sites:
                for query in dub_queries:
                    try:
                        response = tor_session.get(f"{search_engine}/search?q={quote_plus(query)}", timeout=30)
                        if response.status_code == 200:
                            soup = BeautifulSoup(response.text, 'html.parser')
                            for link in soup.find_all('a', href=True)[:3]:
                                if any(term in link.text.lower() for term in ['anime', 'dub', anime_name.lower()]):
                                    results.append({
                                        'source': 'Tor Anime Community',
                                        'title': link.text.strip(),
                                        'url': link['href'],
                                        'snippet': 'Found via Tor anime community search'
                                    })
                    except Exception as e:
                        continue
                    
        except Exception as e:
            print(f"Tor anime community search failed: {e}")
        
        return results

def main():
    print("🎌 Comprehensive Anime English Dub Release Finder")
    print("=" * 50)
    
    # Get user input
    anime_name = input("Enter anime name: ").strip()
    if not anime_name:
        print("❌ Please enter a valid anime name")
        return
    
    print("\n🎯 This program specializes in finding ENGLISH DUB release dates")
    print("   Searching Japanese studios, creators, social media, and dark web...")
    
    use_tor = input("\nSearch Tor network/dark web? (requires Tor running) (y/n): ").strip().lower()
    search_tor = use_tor in ['y', 'yes', '1', 'true']
    
    print(f"\n🔍 Comprehensive search for: {anime_name}")
    print(f"📺 Focus: English Dub Release Information")
    print(f"🌐 Include Tor/Dark Web: {'Yes' if search_tor else 'No'}")
    print("\nStarting comprehensive search...\n")
    
    finder = AnimeReleaseFinder()
    all_results = []
    
    # Search regular web with dub focus
    print("🌐 Searching regular web for dub info...")
    regular_results = finder.search_regular_web(anime_name, dubbed=True)
    all_results.extend(regular_results)
    print(f"   Found {len(regular_results)} results")
    
    # Search Japanese studios and creators
    print("🇯🇵 Searching Japanese studios and creators...")
    creator_results = finder.search_japanese_creators(anime_name, dubbed=True)
    all_results.extend(creator_results)
    print(f"   Found {len(creator_results)} results")
    
    # Search social media for announcements
    print("📱 Searching social media for dub announcements...")
    social_results = finder.search_social_media(anime_name, dubbed=True)
    all_results.extend(social_results)
    print(f"   Found {len(social_results)} results")
    
    # Search streaming platforms
    print("📺 Searching streaming platforms...")
    streaming_results = finder.search_streaming_platforms(anime_name, dubbed=True)
    all_results.extend(streaming_results)
    print(f"   Found {len(streaming_results)} results")
    
    # Search Japanese anime sites
    print("🎌 Searching Japanese anime sites...")
    japanese_results = finder.search_japanese_sites(anime_name, dubbed=True)
    all_results.extend(japanese_results)
    print(f"   Found {len(japanese_results)} results")
    
    # Search Tor network if requested
    if search_tor:
        print("🧅 Searching Tor network and anime communities...")
        tor_results = finder.search_tor_anime_communities(anime_name, dubbed=True)
        all_results.extend(tor_results)
        print(f"   Found {len(tor_results)} results")
    
    # Analyze results with focus on dub information
    if all_results:
        finder.analyze_results(all_results, anime_name, dubbed=True)
    else:
        print("❌ No results found. Try different search terms or check your internet connection.")
    
    print(f"\n✅ Comprehensive search completed!")
    print(f"📊 Total sources searched: {6 if search_tor else 5}")
    print(f"📋 Total results found: {len(all_results)}")
    print(f"🎯 Focus: English dub release information")

if __name__ == "__main__":
    main()