// qquickgraphicsconfiguration.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQuickGraphicsConfiguration
{
%TypeHeaderCode
#include <qquickgraphicsconfiguration.h>
%End

public:
    QQuickGraphicsConfiguration();
    QQuickGraphicsConfiguration(const QQuickGraphicsConfiguration &other);
    ~QQuickGraphicsConfiguration();
    void setDeviceExtensions(const QByteArrayList &extensions);
    QByteArrayList deviceExtensions() const;
    void setDepthBufferFor2D(bool enable);
    bool isDepthBufferEnabledFor2D() const;
%If (Qt_6_1_0 -)
    static QByteArrayList preferredInstanceExtensions();
%End
%If (Qt_6_5_0 -)
    void setDebugLayer(bool enable);
%End
%If (Qt_6_5_0 -)
    bool isDebugLayerEnabled() const;
%End
%If (Qt_6_5_0 -)
    void setDebugMarkers(bool enable);
%End
%If (Qt_6_5_0 -)
    bool isDebugMarkersEnabled() const;
%End
%If (Qt_6_5_0 -)
    void setPreferSoftwareDevice(bool enable);
%End
%If (Qt_6_5_0 -)
    bool prefersSoftwareDevice() const;
%End
%If (Qt_6_5_0 -)
    void setAutomaticPipelineCache(bool enable);
%End
%If (Qt_6_5_0 -)
    bool isAutomaticPipelineCacheEnabled() const;
%End
%If (Qt_6_5_0 -)
    void setPipelineCacheSaveFile(const QString &filename);
%End
%If (Qt_6_5_0 -)
    QString pipelineCacheSaveFile() const;
%End
%If (Qt_6_5_0 -)
    void setPipelineCacheLoadFile(const QString &filename);
%End
%If (Qt_6_5_0 -)
    QString pipelineCacheLoadFile() const;
%End
%If (Qt_6_6_0 -)
    void setTimestamps(bool enable);
%End
%If (Qt_6_6_0 -)
    bool timestampsEnabled() const;
%End
};
