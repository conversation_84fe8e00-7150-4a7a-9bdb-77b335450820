"""
Stock Details Dialog for the Stock Tracker application.
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QFrame, QGroupBox, QSizePolicy,
    QTabWidget, QWidget, QGraphicsDropShadowEffect
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QColor

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.patheffects as path_effects
import matplotlib.colors as mcolors
import numpy as np
from datetime import datetime, timedelta
from scipy.interpolate import make_interp_spline

from ui.styles import Styles
from ui.enhanced_market_chart import EnhancedMarketChart

class StockDetailsDialog(QDialog):
    """
    Dialog for displaying detailed information about a stock.
    """

    def __init__(self, stock, stock_service, ai_service, config, parent=None):
        """
        Initialize the stock details dialog.

        Args:
            stock: The stock to display details for.
            stock_service: The stock service.
            ai_service: The AI service.
            config: The application configuration.
            parent: The parent widget.
        """
        super().__init__(parent)

        self.stock = stock
        self.stock_service = stock_service
        self.ai_service = ai_service
        self.config = config

        # Setup UI
        self.setup_ui()

        # Update UI with stock data
        self.update_stock_details()
        self.update_ai_predictions()

    def setup_ui(self):
        """Setup the UI components."""
        # Set window title and size
        self.setWindowTitle(f"Stock Details: {self.stock.symbol}")
        self.resize(900, 600)  # Reduced height from 700 to 600

        # Apply theme
        self.setStyleSheet(Styles.get_app_stylesheet(self.config.dark_mode))

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # Create tab widget with modern styling
        tab_widget = QTabWidget()
        tab_widget.setDocumentMode(True)  # More modern look
        tab_widget.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {Styles.get_card_bg_color(self.config.dark_mode)};
                background-color: {Styles.get_card_bg_color(self.config.dark_mode)};
                border-radius: 5px;
            }}
            QTabBar::tab {{
                background-color: {Styles.DARK_BG if self.config.dark_mode else Styles.LIGHT_BG};
                color: {Styles.DARK_TEXT if self.config.dark_mode else Styles.LIGHT_TEXT};
                padding: 8px 16px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                margin-right: 2px;
            }}
            QTabBar::tab:selected {{
                background-color: {Styles.get_accent_color(self.config.dark_mode)};
                color: white;
            }}
        """)

        # Create Stock Details tab
        stock_details_tab = QWidget()
        stock_details_layout = QVBoxLayout(stock_details_tab)
        stock_details_layout.setContentsMargins(15, 15, 15, 15)
        stock_details_layout.setSpacing(15)

        # Stock info - modern card style
        stock_info_card = self.create_card_frame("Stock Information")
        stock_info_layout = QVBoxLayout(stock_info_card)
        stock_info_layout.setContentsMargins(20, 20, 20, 20)

        self.stock_info = QLabel("Loading stock details...")
        self.stock_info.setWordWrap(True)
        self.stock_info.setTextFormat(Qt.TextFormat.RichText)
        self.stock_info.setStyleSheet(f"font-size: 14px; color: {Styles.get_text_color(self.config.dark_mode)};")
        self.stock_info.setMinimumHeight(150)  # Set minimum height for info
        stock_info_layout.addWidget(self.stock_info)

        stock_details_layout.addWidget(stock_info_card)

        # Stock performance chart - modern card style
        chart_card = self.create_card_frame("Performance History")
        chart_layout = QVBoxLayout(chart_card)
        chart_layout.setContentsMargins(20, 20, 20, 20)

        # Use EnhancedMarketChart instead of StockPerformanceChart
        self.stock_chart = ModernStockPerformanceChart(self.config)
        chart_layout.addWidget(self.stock_chart)

        stock_details_layout.addWidget(chart_card)

        # Add Stock Details tab
        tab_widget.addTab(stock_details_tab, "Stock Details")

        # Create AI Predictions tab
        ai_tab = QWidget()
        ai_layout = QVBoxLayout(ai_tab)
        ai_layout.setContentsMargins(15, 15, 15, 15)
        ai_layout.setSpacing(15)

        # AI info - modern card style
        ai_info_card = self.create_card_frame("AI Analysis")
        ai_info_layout = QVBoxLayout(ai_info_card)
        ai_info_layout.setContentsMargins(20, 20, 20, 20)

        self.ai_info = QLabel("Loading AI predictions...")
        self.ai_info.setWordWrap(True)
        self.ai_info.setTextFormat(Qt.TextFormat.RichText)
        self.ai_info.setStyleSheet(f"font-size: 14px; color: {Styles.get_text_color(self.config.dark_mode)};")
        self.ai_info.setMinimumHeight(150)  # Set minimum height for info
        ai_info_layout.addWidget(self.ai_info)

        ai_layout.addWidget(ai_info_card)

        # AI prediction chart - modern card style
        ai_chart_card = self.create_card_frame("Price Prediction")
        ai_chart_layout = QVBoxLayout(ai_chart_card)
        ai_chart_layout.setContentsMargins(20, 20, 20, 20)

        # Use ModernAIPredictionChart instead of AIPredictionChart
        self.ai_chart = ModernAIPredictionChart(self.config)
        ai_chart_layout.addWidget(self.ai_chart)

        ai_layout.addWidget(ai_chart_card)

        # Add AI Predictions tab
        tab_widget.addTab(ai_tab, "AI Predictions")

        # Add tab widget to main layout
        main_layout.addWidget(tab_widget)

        # Add close button with modern styling
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        close_button = QPushButton("Close")
        close_button.setFixedWidth(100)
        close_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {Styles.get_accent_color(self.config.dark_mode)};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {Styles.DARK_ACCENT if self.config.dark_mode else Styles.LIGHT_ACCENT};
            }}
            QPushButton:pressed {{
                background-color: #0D47A1;
            }}
        """)
        close_button.clicked.connect(self.accept)
        button_layout.addWidget(close_button)

        main_layout.addLayout(button_layout)

    def create_card_frame(self, title):
        """
        Create a modern card-style frame with title.

        Args:
            title (str): The title for the card.

        Returns:
            QFrame: The created card frame.
        """
        # Create frame
        card = QFrame()
        card.setFrameShape(QFrame.Shape.StyledPanel)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {Styles.get_card_bg_color(self.config.dark_mode)};
                border-radius: 8px;
                border: none;
            }}
        """)

        # Add drop shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 2)
        card.setGraphicsEffect(shadow)

        # Create layout for the card
        layout = QVBoxLayout(card)
        layout.setContentsMargins(0, 0, 0, 0)

        # Add title label
        title_label = QLabel(title)
        title_label.setObjectName("subheading")
        title_label.setStyleSheet(f"""
            font-size: 16px;
            font-weight: bold;
            color: {Styles.get_text_color(self.config.dark_mode)};
            background-color: {Styles.get_card_bg_color(self.config.dark_mode)};
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            padding: 10px 15px;
            border-bottom: 1px solid {'#333333' if self.config.dark_mode else '#E0E0E0'};
        """)

        # Add title to layout
        layout.addWidget(title_label)

        # Create content widget
        content = QWidget()
        content.setObjectName("card_content")
        layout.addWidget(content)

        return card

    def update_stock_details(self):
        """Update the stock details."""
        # Format stock info
        stock = self.stock

        info_text = f"""
        <h2>{stock.name} ({stock.symbol})</h2>
        <p><b>Shares:</b> {stock.shares:.2f}</p>
        <p><b>Purchase Price:</b> ${stock.purchase_price:.2f}</p>
        <p><b>Purchase Date:</b> {stock.purchase_date.split('T')[0]}</p>
        <p><b>Total Cost:</b> ${stock.total_cost:.2f}</p>
        <p><b>Current Price:</b> ${stock.current_price:.2f}</p>
        <p><b>Total Value:</b> ${stock.total_value:.2f}</p>
        """

        # Add profit/loss info with color
        if stock.profit_loss > 0:
            color = Styles.get_positive_color(self.config.dark_mode)
            info_text += f"""
            <p><b>Profit/Loss:</b> <span style="color: {color};">+${stock.profit_loss:.2f} (+{stock.profit_loss_percent:.2f}%)</span></p>
            """
        elif stock.profit_loss < 0:
            color = Styles.get_negative_color(self.config.dark_mode)
            info_text += f"""
            <p><b>Profit/Loss:</b> <span style="color: {color};">${stock.profit_loss:.2f} ({stock.profit_loss_percent:.2f}%)</span></p>
            """
        else:
            color = Styles.get_neutral_color(self.config.dark_mode)
            info_text += f"""
            <p><b>Profit/Loss:</b> <span style="color: {color};">$0.00 (0.00%)</span></p>
            """

        # Add daily change info with color
        if stock.daily_change > 0:
            color = Styles.get_positive_color(self.config.dark_mode)
            info_text += f"""
            <p><b>Daily Change:</b> <span style="color: {color};">+${stock.daily_change:.2f} (+{stock.daily_change_percent:.2f}%)</span></p>
            """
        elif stock.daily_change < 0:
            color = Styles.get_negative_color(self.config.dark_mode)
            info_text += f"""
            <p><b>Daily Change:</b> <span style="color: {color};">${stock.daily_change:.2f} ({stock.daily_change_percent:.2f}%)</span></p>
            """
        else:
            color = Styles.get_neutral_color(self.config.dark_mode)
            info_text += f"""
            <p><b>Daily Change:</b> <span style="color: {color};">$0.00 (0.00%)</span></p>
            """

        # Add last updated info
        if stock.last_updated:
            last_updated = stock.last_updated.split('T')[0]
            info_text += f"""
            <p><b>Last Updated:</b> {last_updated}</p>
            """

        self.stock_info.setText(info_text)

        # Update stock chart
        performance_history = self.stock_service.get_stock_performance_history(stock)
        self.stock_chart.update_chart(performance_history)

    def update_ai_predictions(self):
        """Update the AI predictions."""
        # Get predictions
        prediction = self.ai_service.predict_stock_price(self.stock)

        if not prediction or not prediction['prediction']:
            self.ai_info.setText("<h3>Not enough data for prediction</h3>")
            self.ai_chart.clear_chart()
            return

        # Format prediction info
        confidence = prediction['confidence'] * 100
        recommendation = prediction['recommendation']
        price_change = prediction['price_change_percent']

        # Set color based on recommendation
        if recommendation in ['Strong Buy', 'Buy']:
            color = Styles.get_positive_color(self.config.dark_mode)
        elif recommendation in ['Hold']:
            color = Styles.get_neutral_color(self.config.dark_mode)
        else:
            color = Styles.get_negative_color(self.config.dark_mode)

        info_text = f"""
        <h2>AI Prediction for {self.stock.symbol}</h2>
        <p><b>Confidence:</b> {confidence:.2f}%</p>
        <p><b>Recommendation:</b> <span style="color: {color};">{recommendation}</span></p>
        """

        # Add price change info with color
        if price_change > 0:
            color = Styles.get_positive_color(self.config.dark_mode)
            info_text += f"""
            <p><b>Predicted Price Change:</b> <span style="color: {color};">+{price_change:.2f}%</span></p>
            """
        elif price_change < 0:
            color = Styles.get_negative_color(self.config.dark_mode)
            info_text += f"""
            <p><b>Predicted Price Change:</b> <span style="color: {color};">{price_change:.2f}%</span></p>
            """
        else:
            color = Styles.get_neutral_color(self.config.dark_mode)
            info_text += f"""
            <p><b>Predicted Price Change:</b> <span style="color: {color};">0.00%</span></p>
            """

        # Add prediction details
        info_text += f"""
        <p><b>Current Price:</b> ${self.stock.current_price:.2f}</p>
        <p><b>Average Predicted Price:</b> ${prediction['avg_future_price']:.2f}</p>
        <p><b>Prediction Period:</b> Next 7 days</p>
        <br>
        <h3>Sentiment Analysis</h3>
        <p>{self.ai_service.get_stock_sentiment(self.stock)}</p>
        """

        self.ai_info.setText(info_text)

        # Update AI chart
        self.ai_chart.update_chart(self.stock, prediction['prediction'])


class ModernStockPerformanceChart(QWidget):
    """
    Widget for displaying a modern stock performance chart with enhanced visuals.
    """

    def __init__(self, config):
        """
        Initialize the modern stock performance chart widget.

        Args:
            config: The application configuration.
        """
        super().__init__()

        self.config = config

        # Setup UI
        self.setup_ui()

    def setup_ui(self):
        """Setup the UI components."""
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Create figure and canvas with higher DPI for better quality
        self.figure = Figure(dpi=100)  # Removed fixed figsize to allow dynamic resizing
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.canvas.setMinimumHeight(250)  # Increased minimum height for better visibility

        # Set the figure background color based on theme
        self.apply_theme()

        # Create subplot with proper padding to ensure everything is visible
        self.ax = self.figure.add_subplot(111)

        # Add padding to ensure the plot fits within the frame and is positioned correctly
        self.figure.subplots_adjust(left=0.12, right=0.88, top=0.85, bottom=0.25)

        # Add to layout with stretch to fill available space
        layout.addWidget(self.canvas, 1)  # Add stretch factor

        # Connect resize event to update figure size
        self.canvas.mpl_connect('resize_event', self.on_resize)

        # Initial empty chart
        self.clear_chart()

    def apply_theme(self):
        """Apply the current theme to the chart."""
        # Set the figure background color based on theme
        if self.config.dark_mode:
            # Dark theme with gradient background
            self.figure.patch.set_facecolor('#121212')  # Dark background
            plt.style.use('dark_background')
            self.ax.set_facecolor('#1E1E1E')  # Slightly lighter than the background
        else:
            # Light theme
            self.figure.patch.set_facecolor('#F5F5F5')  # Light background
            plt.style.use('default')
            self.ax.set_facecolor('#FFFFFF')  # White background for the chart

    def update_chart(self, performance_history):
        """
        Update the stock performance chart with new data.

        Args:
            performance_history (dict): A dictionary mapping dates to prices.
        """
        # Apply theme
        self.apply_theme()

        # Clear the current chart
        self.ax.clear()

        # Set text color based on theme
        textcolor = 'white' if self.config.dark_mode else '#333333'

        if not performance_history:
            self.clear_chart()
            return

        # Extract dates and prices
        dates = []
        prices = []
        for date_str, price in sorted(performance_history.items()):
            dates.append(datetime.fromisoformat(date_str.replace('Z', '+00:00')))
            prices.append(price)

        # Determine color based on trend (green for up, red for down)
        if prices[-1] > prices[0]:
            color = '#4CAF50'  # Green for upward trend
            gradient_colors = ['#1B5E20', '#4CAF50', '#A5D6A7']
        else:
            color = '#FF5252'  # Red for downward trend
            gradient_colors = ['#B71C1C', '#FF5252', '#FFCDD2']

        # Create smooth curve for better visualization
        if len(dates) > 2:
            # Convert dates to numeric values for interpolation
            x = np.array([(d - dates[0]).total_seconds() for d in dates])
            y = np.array(prices)

            # Create smooth curve with more points
            x_smooth = np.linspace(x.min(), x.max(), 300)
            try:
                # Use spline interpolation for smooth curve
                spline = make_interp_spline(x, y, k=min(3, len(x)-1))
                y_smooth = spline(x_smooth)

                # Convert back to datetime for plotting
                x_smooth_dates = [dates[0] + timedelta(seconds=s) for s in x_smooth]
            except Exception:
                # Fallback to original data if interpolation fails
                x_smooth_dates = dates
                y_smooth = prices
        else:
            # Not enough points for interpolation
            x_smooth_dates = dates
            y_smooth = prices

        # Add grid with custom styling
        self.ax.grid(True, linestyle='--', alpha=0.3, zorder=0)

        # Plot the main line with glow effect
        main_line, = self.ax.plot(x_smooth_dates, y_smooth, color=color, linestyle='-',
                                 linewidth=2.5, label='Price', zorder=10)

        # Add glow effect to the line
        main_line.set_path_effects([
            path_effects.SimpleLineShadow(offset=(0, 0), shadow_color=color, alpha=0.6, rho=8),
            path_effects.Normal()
        ])

        # Add a subtle fill below the line
        self.ax.fill_between(x_smooth_dates, y_smooth, min(y_smooth),
                            alpha=0.15, color=color, zorder=5)

        # Add markers at start and end points
        self.ax.plot(dates[0], prices[0], 'o', color='#2196F3', markersize=8,
                    markeredgecolor='white', markeredgewidth=1, zorder=15)
        self.ax.plot(dates[-1], prices[-1], 'o', color=color, markersize=8,
                    markeredgecolor='white', markeredgewidth=1, zorder=15)

        # Format x-axis with better date formatting
        self.ax.xaxis.set_major_formatter(plt.matplotlib.dates.DateFormatter('%b %d'))
        plt.setp(self.ax.xaxis.get_majorticklabels(), rotation=30, ha='right')

        # Format y-axis with dollar signs
        self.ax.yaxis.set_major_formatter(plt.matplotlib.ticker.StrMethodFormatter('${x:.2f}'))

        # Set labels and title with enhanced styling
        self.ax.set_xlabel('Date', color=textcolor, fontweight='bold', fontsize=10)
        self.ax.set_ylabel('Price ($)', color=textcolor, fontweight='bold', fontsize=10)

        title_obj = self.ax.set_title('7-Day Performance History', color=textcolor,
                                     fontweight='bold', fontsize=12, pad=10)
        title_obj.set_path_effects([
            path_effects.withStroke(linewidth=3, foreground='black' if self.config.dark_mode else 'white')
        ])

        # Add price change annotation
        price_change = prices[-1] - prices[0]
        price_change_pct = (price_change / prices[0]) * 100

        change_text = f"Change: {'+'if price_change >= 0 else ''}{price_change:.2f} ({'+' if price_change_pct >= 0 else ''}{price_change_pct:.2f}%)"
        self.ax.annotate(change_text,
                        xy=(0.98, 0.05),
                        xycoords='axes fraction',
                        horizontalalignment='right',
                        verticalalignment='bottom',
                        fontsize=10,
                        fontweight='bold',
                        color=color,
                        bbox=dict(boxstyle="round,pad=0.3", fc='#1E1E1E' if self.config.dark_mode else 'white',
                                 ec=color, alpha=0.7))

        # Adjust layout
        try:
            self.figure.tight_layout(pad=1.2, h_pad=1.0, w_pad=1.0)
        except Warning:
            # If tight_layout fails, use a fixed padding
            self.figure.subplots_adjust(left=0.12, right=0.95, bottom=0.25, top=0.85)

        # Redraw canvas
        self.canvas.draw()

    def clear_chart(self):
        """Clear the stock performance chart."""
        # Apply theme
        self.apply_theme()

        # Clear the current chart
        self.ax.clear()

        # Set text color based on theme
        textcolor = 'white' if self.config.dark_mode else '#333333'

        # Set title with enhanced styling
        title_obj = self.ax.set_title('Stock Performance', color=textcolor, fontsize=14, fontweight='bold')
        title_obj.set_path_effects([
            path_effects.withStroke(linewidth=3, foreground='black' if self.config.dark_mode else 'white')
        ])

        # Add text for empty chart with enhanced styling
        empty_text = self.ax.text(
            0.5, 0.5,
            'No performance data available',
            horizontalalignment='center',
            verticalalignment='center',
            transform=self.ax.transAxes,
            color=textcolor,
            fontsize=12,
            fontweight='bold'
        )

        # Add glow effect to the text
        empty_text.set_path_effects([
            path_effects.withStroke(linewidth=3, foreground='black' if self.config.dark_mode else 'white')
        ])

        # Hide axes
        self.ax.axis('off')

        # Redraw canvas
        self.canvas.draw()

    def on_resize(self, event):
        """
        Handle resize events to update the figure size and redraw.

        Args:
            event: The resize event.
        """
        # Adjust the figure when the canvas is resized
        self.figure.tight_layout()

        # Redraw the canvas
        self.canvas.draw()


class AIPredictionChart(QWidget):
    """
    Widget for displaying an AI prediction chart.
    """

    def __init__(self, config):
        """
        Initialize the AI prediction chart widget.

        Args:
            config: The application configuration.
        """
        super().__init__()

        self.config = config

        # Setup UI
        self.setup_ui()

    def setup_ui(self):
        """Setup the UI components."""
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Create figure and canvas
        self.figure = Figure(figsize=(10, 4), dpi=100)  # Reduced height from 6 to 4
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.canvas.setMinimumHeight(200)  # Reduced minimum height from 300 to 200

        # Set dark mode for matplotlib
        if self.config.dark_mode:
            plt.style.use('dark_background')

        # Create subplot
        self.ax = self.figure.add_subplot(111)

        # Add to layout
        layout.addWidget(self.canvas)

        # Initial empty chart
        self.clear_chart()

    def apply_theme(self):
        """Apply the current theme to the chart."""
        # Set the figure background color based on theme
        if self.config.dark_mode:
            self.figure.patch.set_facecolor('#121212')  # Dark background
            plt.style.use('dark_background')
            self.ax.set_facecolor('#1E1E1E')  # Slightly lighter than the background
        else:
            self.figure.patch.set_facecolor('#F5F5F5')  # Light background
            plt.style.use('default')
            self.ax.set_facecolor('#FFFFFF')  # White background for the chart

    def update_chart(self, stock, prediction):
        """
        Update the AI prediction chart with new data.

        Args:
            stock: The stock object.
            prediction (dict): A dictionary mapping dates to predicted prices.
        """
        # Apply theme
        self.apply_theme()

        # Clear the current chart
        self.ax.clear()

        # Set text color based on theme
        textcolor = 'white' if self.config.dark_mode else 'black'

        if not prediction:
            self.clear_chart()
            return

        # Extract dates and prices
        dates = []
        prices = []
        for date_str, price in sorted(prediction.items()):
            dates.append(datetime.fromisoformat(date_str.replace('Z', '+00:00')))
            prices.append(price)

        # Add current price
        today = datetime.now()
        dates.insert(0, today)
        prices.insert(0, stock.current_price)

        # Plot the data
        self.ax.plot(dates, prices, 'g-', linewidth=2)

        # Add markers
        self.ax.plot(dates[0], prices[0], 'bo', markersize=8, label='Current Price')
        self.ax.plot(dates[1:], prices[1:], 'ro', markersize=8, label='Predicted Price')

        # Add grid
        self.ax.grid(True, linestyle='--', alpha=0.7)

        # Format x-axis
        self.ax.xaxis.set_major_formatter(plt.matplotlib.dates.DateFormatter('%Y-%m-%d'))
        plt.setp(self.ax.xaxis.get_majorticklabels(), rotation=45)

        # Set labels and title
        self.ax.set_xlabel('Date', color=textcolor)
        self.ax.set_ylabel('Price ($)', color=textcolor)
        self.ax.set_title(f'AI Price Prediction for {stock.symbol}', color=textcolor)

        # Add legend
        self.ax.legend()

        # Adjust layout
        try:
            self.figure.tight_layout(pad=1.2, h_pad=1.0, w_pad=1.0)
        except Warning:
            # If tight_layout fails, use a fixed padding
            self.figure.subplots_adjust(left=0.12, right=0.95, bottom=0.15, top=0.9)

        # Redraw canvas
        self.canvas.draw()

    def clear_chart(self):
        """Clear the AI prediction chart."""
        # Apply theme
        self.apply_theme()

        # Clear the current chart
        self.ax.clear()

        # Set text color based on theme
        textcolor = 'white' if self.config.dark_mode else 'black'

        # Set title
        self.ax.set_title('AI Prediction', color=textcolor, fontsize=12, fontweight='bold')

        # Add text for empty chart
        self.ax.text(
            0.5, 0.5,
            'No prediction data available',
            horizontalalignment='center',
            verticalalignment='center',
            transform=self.ax.transAxes,
            color=textcolor,
            fontsize=10
        )

        # Hide axes
        self.ax.axis('off')

        # Redraw canvas
        self.canvas.draw()
