# 📺 Anime Episode Release Finder

A simple Python application to search for anime episode release schedules and streaming information.

## 🚀 Quick Start

### Option 1: GUI Version (Recommended)
```bash
python run_gui.py
```

### Option 2: Command Line Version
```bash
python anime_release_finder.py
```

### Option 3: Simple Main (Basic)
```bash
python main.py
```

## 📦 Installation

1. Install required packages:
```bash
pip install -r requirements.txt
```

Or install manually:
```bash
pip install PyQt6 requests
```

## 🎯 Features

- **GUI Interface**: Easy-to-use graphical interface with dark theme
- **Multiple Sources**: Searches streaming platforms, anime databases, and Japanese sites
- **Progress Tracking**: Real-time search progress with status updates
- **Result Details**: Click on results to view detailed information
- **Tor Support**: Optional Tor/dark web search (simplified in this version)

## 🔍 How to Use

1. **Launch the GUI**: Run `python run_gui.py`
2. **Enter Anime Name**: Type the name of the anime you want to search for
3. **Start Search**: Click "🚀 Start Search" button
4. **View Results**: Click on any result in the list to see details
5. **Visit Sources**: Copy URLs from the details to visit source websites

## 📋 Search Sources

The application searches:
- **Streaming Platforms**: Crunchyroll, Funimation, Netflix, Hulu
- **Anime Databases**: MyAnimeList, AniList, Anime News Network, LiveChart
- **Japanese Sites**: Official Japanese anime sources
- **Web Search**: General web search for episode schedules

## 🛠️ Troubleshooting

### GUI Won't Start
- Make sure PyQt6 is installed: `pip install PyQt6`
- Try the command-line version: `python anime_release_finder.py`

### No Results Found
- Check anime name spelling
- Try different variations of the anime name
- Check your internet connection

### Dependencies Issues
- Install all requirements: `pip install -r requirements.txt`
- Use Python 3.8 or newer

## 📁 Files

- `run_gui.py` - GUI launcher (start here)
- `anime_gui.py` - Main GUI application
- `anime_release_finder.py` - Core search functionality
- `main.py` - Simple command-line version
- `requirements.txt` - Required packages

## 💡 Tips

- Use specific anime names for better results
- Check multiple sources for the most accurate information
- Visit the source URLs for detailed episode schedules
- Some results may be search suggestions rather than direct matches

## 🔧 Technical Notes

This is a simplified version that works with basic dependencies. The search functionality uses web search simulation and direct URL generation for anime sites. For more advanced features, additional packages like `duckduckgo-search` and `beautifulsoup4` can be installed.
