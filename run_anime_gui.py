#!/usr/bin/env python3
"""
Simple launcher for the Anime Release Finder GUI
"""

import sys
import os

# Add current directory to path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from anime_gui import main
    main()
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("\nPlease install required packages:")
    print("pip install PyQt6 requests beautifulsoup4 duckduckgo-search PySocks lxml")
    input("Press Enter to exit...")
except Exception as e:
    print(f"Error running application: {e}")
    input("Press Enter to exit...")