#!/usr/bin/env python3
"""
Anime Release Finder GUI
Simplified PyQt6 interface for searching anime release dates
"""

import sys
import threading
from datetime import datetime, date

try:
    from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                                QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                                QTextEdit, QProgressBar, QCheckBox, QGroupBox,
                                QSplitter, QListWidget, QListWidgetItem, QMessageBox,
                                QTabWidget, QScrollArea, QFrame, QCalendarWidget,
                                QTableWidget, QTableWidgetItem, QHeaderView)
    from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer, QDate
    from PyQt6.QtGui import QFont, QPixmap, QPainter, QIcon, QTextCharFormat
    HAS_PYQT6 = True
except ImportError:
    HAS_PYQT6 = False
    print("PyQt6 not available. Please install with: pip install PyQt6")

if HAS_PYQT6:
    from anime_release_finder import AnimeReleaseFinder

    class SearchWorker(QThread):
        """Worker thread for anime search operations"""
        progress_updated = pyqtSignal(int, str)
        result_found = pyqtSignal(dict)
        search_complete = pyqtSignal(list, list)
        
        def __init__(self, anime_name, use_tor=False):
            super().__init__()
            self.anime_name = anime_name
            self.use_tor = use_tor
            self.finder = AnimeReleaseFinder()
            self.all_results = []
            
        def run(self):
            """Run the comprehensive anime episode release search"""
            try:
                total_steps = 5 if self.use_tor else 4
                current_step = 0
                
                # Step 1: Regular web search for episode schedules
                current_step += 1
                self.progress_updated.emit(int(current_step/total_steps * 100), 
                                         "🌐 Searching for episode release schedules...")
                regular_results = self.finder.search_regular_web(self.anime_name, dubbed=False)
                self.all_results.extend(regular_results)
                for result in regular_results:
                    self.result_found.emit(result)
                
                # Step 2: Streaming platforms
                current_step += 1
                self.progress_updated.emit(int(current_step/total_steps * 100), 
                                         "📺 Searching streaming platforms...")
                streaming_results = self.finder.search_streaming_platforms(self.anime_name, dubbed=False)
                self.all_results.extend(streaming_results)
                for result in streaming_results:
                    self.result_found.emit(result)
                
                # Step 3: Anime schedule databases
                current_step += 1
                self.progress_updated.emit(int(current_step/total_steps * 100), 
                                         "📅 Searching anime schedule databases...")
                schedule_results = self.finder.search_anime_schedule_sites(self.anime_name, dubbed=False)
                self.all_results.extend(schedule_results)
                for result in schedule_results:
                    self.result_found.emit(result)
                
                # Step 4: Japanese anime sites
                current_step += 1
                self.progress_updated.emit(int(current_step/total_steps * 100), 
                                         "🎌 Searching Japanese anime sites...")
                japanese_results = self.finder.search_japanese_sites(self.anime_name, dubbed=False)
                self.all_results.extend(japanese_results)
                for result in japanese_results:
                    self.result_found.emit(result)
                
                # Step 5: Tor network (if enabled)
                if self.use_tor:
                    current_step += 1
                    self.progress_updated.emit(int(current_step/total_steps * 100), 
                                             "🧅 Searching Tor anime communities...")
                    tor_results = self.finder.search_tor_network(self.anime_name, dubbed=False)
                    self.all_results.extend(tor_results)
                    for result in tor_results:
                        self.result_found.emit(result)
                
                # Extract dates and times from all results
                all_dates = []
                for result in self.all_results:
                    dates = self.finder.extract_dates(result['title'] + ' ' + result['snippet'])
                    all_dates.extend(dates)
                
                self.progress_updated.emit(100, "✅ Search completed!")
                self.search_complete.emit(self.all_results, list(set(all_dates)))
                
            except Exception as e:
                self.progress_updated.emit(100, f"❌ Error: {str(e)}")
                self.search_complete.emit([], [])

    class AnimeSearchGUI(QMainWindow):
        """Main GUI window for anime release finder"""
        
        def __init__(self):
            super().__init__()
            self.search_worker = None
            self.results = []
            self.dates = []
            self.setup_ui()
            
        def setup_ui(self):
            """Set up the main UI"""
            self.setWindowTitle("📺 Anime Episode Release Finder")
            self.setGeometry(100, 100, 1200, 600)
            
            # Apply dark theme
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QWidget {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QLineEdit {
                    background-color: #404040;
                    border: 2px solid #555555;
                    border-radius: 5px;
                    padding: 6px;
                    font-size: 13px;
                }
                QLineEdit:focus {
                    border-color: #0078d4;
                }
                QPushButton {
                    background-color: #0078d4;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 16px;
                    font-size: 13px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #106ebe;
                }
                QPushButton:pressed {
                    background-color: #005a9e;
                }
                QPushButton:disabled {
                    background-color: #555555;
                    color: #888888;
                }
                QTextEdit {
                    background-color: #404040;
                    border: 2px solid #555555;
                    border-radius: 5px;
                    padding: 8px;
                    font-size: 11px;
                }
                QListWidget {
                    background-color: #404040;
                    border: 2px solid #555555;
                    border-radius: 5px;
                    font-size: 11px;
                }
                QListWidget::item {
                    padding: 6px;
                    border-bottom: 1px solid #555555;
                    min-height: 16px;
                }
                QListWidget::item:selected {
                    background-color: #0078d4;
                }
                QGroupBox {
                    font-weight: bold;
                    border: 2px solid #555555;
                    border-radius: 5px;
                    margin-top: 8px;
                    padding-top: 8px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                }
                QProgressBar {
                    border: 2px solid #555555;
                    border-radius: 5px;
                    text-align: center;
                    background-color: #404040;
                    font-size: 11px;
                }
                QProgressBar::chunk {
                    background-color: #0078d4;
                    border-radius: 3px;
                }
                QCheckBox {
                    spacing: 8px;
                    font-size: 11px;
                }
                QCheckBox::indicator {
                    width: 16px;
                    height: 16px;
                }
                QCheckBox::indicator:unchecked {
                    border: 2px solid #555555;
                    background-color: #404040;
                    border-radius: 3px;
                }
                QCheckBox::indicator:checked {
                    border: 2px solid #0078d4;
                    background-color: #0078d4;
                    border-radius: 3px;
                }
            """)
            
            # Create central widget
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)
            layout.setContentsMargins(8, 8, 8, 8)
            layout.setSpacing(8)
            
            # Create header
            self.create_header(layout)
            
            # Create search controls
            self.create_search_controls(layout)
            
            # Create progress section
            self.create_progress_section(layout)
            
            # Create results section
            self.create_results_section(layout)
        
        def create_header(self, parent_layout):
            """Create header section"""
            header_frame = QFrame()
            header_frame.setStyleSheet("background-color: #1e1e1e; border-radius: 8px;")
            header_frame.setMaximumHeight(50)
            header_layout = QHBoxLayout(header_frame)
            header_layout.setContentsMargins(8, 4, 8, 4)
            
            # Title
            title_label = QLabel("📺 Anime Episode Release Finder")
            title_font = QFont()
            title_font.setPointSize(14)
            title_font.setBold(True)
            title_label.setFont(title_font)
            title_label.setStyleSheet("color: #0078d4;")
            header_layout.addWidget(title_label)
            
            # Subtitle
            subtitle_label = QLabel("Find episode release schedules and streaming platforms")
            subtitle_font = QFont()
            subtitle_font.setPointSize(9)
            subtitle_label.setFont(subtitle_font)
            subtitle_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            subtitle_label.setStyleSheet("color: #cccccc;")
            header_layout.addWidget(subtitle_label)
            
            parent_layout.addWidget(header_frame)
        
        def create_search_controls(self, parent_layout):
            """Create search input controls"""
            search_group = QGroupBox("🔍 Search Configuration")
            search_group.setMaximumHeight(80)
            search_layout = QHBoxLayout(search_group)
            
            # Anime name input
            name_label = QLabel("Anime Name:")
            name_label.setMinimumWidth(80)
            self.anime_input = QLineEdit()
            self.anime_input.setPlaceholderText("Enter anime name (e.g., 'Attack on Titan', 'Demon Slayer')")
            self.anime_input.returnPressed.connect(self.start_search)
            
            # Search button
            self.search_button = QPushButton("🚀 Start Search")
            self.search_button.clicked.connect(self.start_search)
            self.search_button.setMinimumHeight(32)
            
            # Tor checkbox
            self.tor_checkbox = QCheckBox("🧅 Include Tor/Dark Web")
            self.tor_checkbox.setToolTip("Enable this to search the dark web for additional information")
            
            search_layout.addWidget(name_label)
            search_layout.addWidget(self.anime_input)
            search_layout.addWidget(self.tor_checkbox)
            search_layout.addWidget(self.search_button)
            
            parent_layout.addWidget(search_group)

        def create_progress_section(self, parent_layout):
            """Create progress tracking section"""
            progress_group = QGroupBox("📊 Search Progress")
            progress_group.setMaximumHeight(60)
            progress_layout = QVBoxLayout(progress_group)

            # Progress bar
            self.progress_bar = QProgressBar()
            self.progress_bar.setMinimum(0)
            self.progress_bar.setMaximum(100)
            self.progress_bar.setValue(0)
            self.progress_bar.setTextVisible(True)
            self.progress_bar.setMaximumHeight(24)
            progress_layout.addWidget(self.progress_bar)

            # Status label
            self.status_label = QLabel("Ready to search...")
            self.status_label.setStyleSheet("color: #cccccc; font-size: 12px;")
            progress_layout.addWidget(self.status_label)

            parent_layout.addWidget(progress_group)

        def create_results_section(self, parent_layout):
            """Create results display section"""
            results_group = QGroupBox("📋 Search Results")
            results_layout = QHBoxLayout(results_group)

            # Create splitter for results
            results_splitter = QSplitter(Qt.Orientation.Horizontal)

            # Left panel - Results list
            left_panel = QWidget()
            left_layout = QVBoxLayout(left_panel)

            # Results count
            self.results_count_label = QLabel("No results yet")
            self.results_count_label.setStyleSheet("color: #cccccc; font-weight: bold; font-size: 12px;")
            left_layout.addWidget(self.results_count_label)

            # Results list
            self.results_list = QListWidget()
            self.results_list.itemClicked.connect(self.show_result_details)
            left_layout.addWidget(self.results_list)

            # Right panel - Result details
            right_panel = QWidget()
            right_layout = QVBoxLayout(right_panel)

            details_label = QLabel("📄 Result Details")
            details_label.setStyleSheet("color: #cccccc; font-weight: bold; font-size: 12px;")
            right_layout.addWidget(details_label)

            self.details_text = QTextEdit()
            self.details_text.setPlaceholderText("Select a result from the list to view details...")
            right_layout.addWidget(self.details_text)

            # Add panels to splitter
            results_splitter.addWidget(left_panel)
            results_splitter.addWidget(right_panel)
            results_splitter.setSizes([500, 500])

            results_layout.addWidget(results_splitter)
            parent_layout.addWidget(results_group)

        def start_search(self):
            """Start the anime search"""
            anime_name = self.anime_input.text().strip()
            if not anime_name:
                QMessageBox.warning(self, "Input Error", "Please enter an anime name!")
                return

            # Clear previous results
            self.results = []
            self.dates = []
            self.results_list.clear()
            self.details_text.clear()

            # Update UI
            self.search_button.setEnabled(False)
            self.search_button.setText("🔍 Searching...")
            self.progress_bar.setValue(0)
            self.results_count_label.setText("Starting search...")

            # Start search worker
            use_tor = self.tor_checkbox.isChecked()
            self.search_worker = SearchWorker(anime_name, use_tor)
            self.search_worker.progress_updated.connect(self.update_progress)
            self.search_worker.result_found.connect(self.add_result)
            self.search_worker.search_complete.connect(self.search_finished)
            self.search_worker.start()

        def update_progress(self, value, message):
            """Update progress bar and status"""
            self.progress_bar.setValue(value)
            self.status_label.setText(message)

        def add_result(self, result):
            """Add a new result to the list"""
            self.results.append(result)

            # Add to results list
            item_text = f"[{result['source']}] {result['title'][:80]}..."
            item = QListWidgetItem(item_text)
            item.setData(Qt.ItemDataRole.UserRole, len(self.results) - 1)
            self.results_list.addItem(item)

            # Update count
            self.results_count_label.setText(f"Found {len(self.results)} results")

        def search_finished(self, all_results, dates):
            """Handle search completion"""
            self.results = all_results
            self.dates = dates

            # Update UI
            self.search_button.setEnabled(True)
            self.search_button.setText("🚀 Start Search")
            self.progress_bar.setValue(100)
            self.status_label.setText(f"✅ Search completed! Found {len(all_results)} results")

            if not all_results:
                self.results_count_label.setText("❌ No results found")
                self.details_text.setText("No results found. Try:\n• Different anime name spelling\n• Check internet connection\n• Enable Tor search for more sources")
            else:
                self.results_count_label.setText(f"✅ Found {len(all_results)} results")

        def show_result_details(self, item):
            """Show details for selected result"""
            index = item.data(Qt.ItemDataRole.UserRole)
            if index is not None and 0 <= index < len(self.results):
                result = self.results[index]

                details = f"""🔍 SOURCE: {result['source']}

📰 TITLE: {result['title']}

🔗 URL: {result['url']}

📝 DESCRIPTION:
{result['snippet']}

---
💡 TIP: Copy the URL to visit the source page for more information."""

                self.details_text.setText(details.strip())

    def main():
        """Main function to run the GUI"""
        if not HAS_PYQT6:
            print("❌ PyQt6 is required to run the GUI")
            print("Install with: pip install PyQt6")
            input("Press Enter to exit...")
            return

        app = QApplication(sys.argv)
        app.setApplicationName("Anime Release Finder")

        # Create and show main window
        window = AnimeSearchGUI()
        window.show()

        # Start event loop
        sys.exit(app.exec())

else:
    def main():
        """Fallback main function when PyQt6 is not available"""
        print("❌ PyQt6 is not available")
        print("Please install PyQt6 to use the GUI:")
        print("pip install PyQt6")
        print("\nAlternatively, run the command-line version:")
        print("python anime_release_finder.py")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
