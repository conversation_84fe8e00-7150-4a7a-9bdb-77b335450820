#!/usr/bin/env python3
"""
Anime Release Finder GUI
PyQt6 interface for searching anime English dub release dates
"""

import sys
import threading
from datetime import datetime, date
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                            QTextEdit, QProgressBar, QCheckBox, QGroupBox,
                            QSplitter, QListWidget, QListWidgetItem, QMessageBox,
                            QTabWidget, QScrollArea, QFrame, QCalendarWidget,
                            QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer, QDate
from PyQt6.QtGui import QFont, QPixmap, QPainter, QIcon, QTextCharFormat

from anime_release_finder import AnimeReleaseFinder

class SearchWorker(QThread):
    """Worker thread for anime search operations"""
    progress_updated = pyqtSignal(int, str)
    result_found = pyqtSignal(dict)
    search_complete = pyqtSignal(list, list)
    
    def __init__(self, anime_name, use_tor=False):
        super().__init__()
        self.anime_name = anime_name
        self.use_tor = use_tor
        self.finder = AnimeReleaseFinder()
        self.all_results = []
        
    def run(self):
        """Run the comprehensive anime episode release search"""
        try:
            total_steps = 5 if self.use_tor else 4
            current_step = 0
            
            # Step 1: Regular web search for episode schedules
            current_step += 1
            self.progress_updated.emit(int(current_step/total_steps * 100), 
                                     "🌐 Searching for episode release schedules...")
            regular_results = self.finder.search_regular_web(self.anime_name, dubbed=False)
            self.all_results.extend(regular_results)
            for result in regular_results:
                self.result_found.emit(result)
            
            # Step 2: Streaming platforms
            current_step += 1
            self.progress_updated.emit(int(current_step/total_steps * 100), 
                                     "📺 Searching streaming platforms...")
            streaming_results = self.finder.search_streaming_platforms(self.anime_name, dubbed=False)
            self.all_results.extend(streaming_results)
            for result in streaming_results:
                self.result_found.emit(result)
            
            # Step 3: Anime schedule databases
            current_step += 1
            self.progress_updated.emit(int(current_step/total_steps * 100), 
                                     "📅 Searching anime schedule databases...")
            schedule_results = self.finder.search_anime_schedule_sites(self.anime_name, dubbed=False)
            self.all_results.extend(schedule_results)
            for result in schedule_results:
                self.result_found.emit(result)
            
            # Step 4: Japanese anime sites
            current_step += 1
            self.progress_updated.emit(int(current_step/total_steps * 100), 
                                     "🎌 Searching Japanese anime sites...")
            japanese_results = self.finder.search_japanese_sites(self.anime_name, dubbed=False)
            self.all_results.extend(japanese_results)
            for result in japanese_results:
                self.result_found.emit(result)
            
            # Step 5: Tor network (if enabled)
            if self.use_tor:
                current_step += 1
                self.progress_updated.emit(int(current_step/total_steps * 100), 
                                         "🧅 Searching Tor anime communities...")
                tor_results = self.finder.search_tor_network(self.anime_name, dubbed=False)
                self.all_results.extend(tor_results)
                for result in tor_results:
                    self.result_found.emit(result)
            
            # Extract dates and times from all results
            all_dates = []
            for result in self.all_results:
                dates = self.finder.extract_dates(result['title'] + ' ' + result['snippet'])
                all_dates.extend(dates)
            
            self.progress_updated.emit(100, "✅ Search completed!")
            self.search_complete.emit(self.all_results, list(set(all_dates)))
            
        except Exception as e:
            self.progress_updated.emit(100, f"❌ Error: {str(e)}")
            self.search_complete.emit([], [])

class AnimeSearchGUI(QMainWindow):
    """Main GUI window for anime release finder"""
    
    def __init__(self):
        super().__init__()
        self.search_worker = None
        self.results = []
        self.dates = []
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the main UI"""
        self.setWindowTitle("📺 Anime Episode Release Finder")
        self.setGeometry(100, 100, 1600, 700)
        
        # Apply dark theme
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QLineEdit {
                background-color: #404040;
                border: 2px solid #555555;
                border-radius: 5px;
                padding: 6px;
                font-size: 13px;
            }
            QLineEdit:focus {
                border-color: #0078d4;
            }
            QPushButton {
                background-color: #0078d4;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #555555;
                color: #888888;
            }
            QTextEdit {
                background-color: #404040;
                border: 2px solid #555555;
                border-radius: 5px;
                padding: 8px;
                font-size: 11px;
            }
            QListWidget {
                background-color: #404040;
                border: 2px solid #555555;
                border-radius: 5px;
                font-size: 11px;
            }
            QListWidget::item {
                padding: 6px;
                border-bottom: 1px solid #555555;
                min-height: 16px;
            }
            QListWidget::item:selected {
                background-color: #0078d4;
            }
            QTableWidget {
                background-color: #404040;
                border: 2px solid #555555;
                border-radius: 5px;
                gridline-color: #555555;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #555555;
            }
            QTableWidget::item:selected {
                background-color: #0078d4;
            }
            QHeaderView::section {
                background-color: #333333;
                color: #ffffff;
                padding: 6px;
                border: 1px solid #555555;
                font-weight: bold;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 5px;
                margin-top: 8px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QProgressBar {
                border: 2px solid #555555;
                border-radius: 5px;
                text-align: center;
                background-color: #404040;
                font-size: 11px;
            }
            QProgressBar::chunk {
                background-color: #0078d4;
                border-radius: 3px;
            }
            QCheckBox {
                spacing: 8px;
                font-size: 11px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #555555;
                background-color: #404040;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #0078d4;
                background-color: #0078d4;
                border-radius: 3px;
            }
            QTabWidget::pane {
                border: 2px solid #555555;
                border-radius: 5px;
                background-color: #2b2b2b;
            }
            QTabBar::tab {
                background-color: #404040;
                color: #ffffff;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: #0078d4;
            }
            QTabBar::tab:hover {
                background-color: #555555;
            }
            QCalendarWidget {
                background-color: #404040;
                border: 2px solid #555555;
                border-radius: 5px;
            }
            QCalendarWidget QToolButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 4px;
            }
            QCalendarWidget QMenu {
                background-color: #404040;
                color: white;
            }
            QCalendarWidget QSpinBox {
                background-color: #404040;
                color: white;
                border: 1px solid #555555;
            }
            QCalendarWidget QAbstractItemView {
                background-color: #404040;
                color: white;
                selection-background-color: #0078d4;
            }
        """)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)
        
        # Create compact header
        self.create_compact_header(layout)
        
        # Create search controls
        self.create_search_controls(layout)
        
        # Create progress section
        self.create_progress_section(layout)
        
        # Create main content with tabs
        self.create_tabbed_content(layout)
    
    def create_compact_header(self, parent_layout):
        """Create a compact header section"""
        header_frame = QFrame()
        header_frame.setStyleSheet("background-color: #1e1e1e; border-radius: 8px;")
        header_frame.setMaximumHeight(50)
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(8, 4, 8, 4)
        header_layout.setSpacing(10)
        
        # Title on the left
        title_label = QLabel("📺 Anime Episode Release Finder")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #0078d4; margin: 0px; padding: 0px;")
        header_layout.addWidget(title_label)
        
        # Subtitle on the right
        subtitle_label = QLabel("Find episode release schedules, air dates, and streaming platforms")
        subtitle_font = QFont()
        subtitle_font.setPointSize(9)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        subtitle_label.setStyleSheet("color: #cccccc; margin: 0px; padding: 0px;")
        header_layout.addWidget(subtitle_label)
        
        parent_layout.addWidget(header_frame)
    
    def create_search_controls(self, parent_layout):
        """Create search input controls"""
        search_group = QGroupBox("🔍 Search Configuration")
        search_group.setMaximumHeight(100)  # Limit height
        search_layout = QVBoxLayout(search_group)
        search_layout.setSpacing(6)
        
        # Anime name input
        name_layout = QHBoxLayout()
        name_label = QLabel("Anime Name:")
        name_label.setMinimumWidth(80)
        self.anime_input = QLineEdit()
        self.anime_input.setPlaceholderText("Enter anime name (e.g., 'Attack on Titan', 'Demon Slayer')")
        self.anime_input.returnPressed.connect(self.start_search)
        
        # Search button and options in same row
        self.search_button = QPushButton("🚀 Start Search")
        self.search_button.clicked.connect(self.start_search)
        self.search_button.setMinimumHeight(32)
        
        self.tor_checkbox = QCheckBox("🧅 Include Tor/Dark Web")
        self.tor_checkbox.setToolTip("Enable this to search the dark web for additional information")
        
        name_layout.addWidget(name_label)
        name_layout.addWidget(self.anime_input)
        name_layout.addWidget(self.tor_checkbox)
        name_layout.addWidget(self.search_button)
        search_layout.addLayout(name_layout)
        
        parent_layout.addWidget(search_group)
    
    def create_progress_section(self, parent_layout):
        """Create progress tracking section"""
        progress_group = QGroupBox("📊 Search Progress")
        progress_group.setMaximumHeight(80)  # Limit height
        progress_layout = QVBoxLayout(progress_group)
        progress_layout.setSpacing(4)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setMaximumHeight(24)
        progress_layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Ready to search...")
        self.status_label.setStyleSheet("color: #cccccc; font-size: 12px; margin: 2px;")
        progress_layout.addWidget(self.status_label)
        
        parent_layout.addWidget(progress_group)
    
    def create_tabbed_content(self, parent_layout):
        """Create the main tabbed content area"""
        # Create tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setMinimumHeight(350)  # Reduced minimum height
        
        # Tab 1: Search Results
        self.create_results_tab()
        
        # Tab 2: Release Calendar
        self.create_calendar_tab()
        
        # Tab 3: Detailed Results Table
        self.create_table_tab()
        
        parent_layout.addWidget(self.tab_widget)
    
    def create_results_tab(self):
        """Create the search results tab"""
        results_widget = QWidget()
        results_layout = QHBoxLayout(results_widget)
        results_layout.setContentsMargins(4, 4, 4, 4)
        
        # Create splitter for results
        results_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left panel - Results list
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(4, 4, 4, 4)
        
        # Results count
        self.results_count_label = QLabel("No results yet")
        self.results_count_label.setStyleSheet("color: #cccccc; font-weight: bold; font-size: 12px; margin: 2px;")
        left_layout.addWidget(self.results_count_label)
        
        # Results list
        self.results_list = QListWidget()
        self.results_list.itemClicked.connect(self.show_result_details)
        left_layout.addWidget(self.results_list)
        
        # Right panel - Result details
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(4, 4, 4, 4)
        
        details_label = QLabel("📄 Result Details")
        details_label.setStyleSheet("color: #cccccc; font-weight: bold; font-size: 12px; margin: 2px;")
        right_layout.addWidget(details_label)
        
        self.details_text = QTextEdit()
        self.details_text.setPlaceholderText("Select a result from the list to view details...")
        right_layout.addWidget(self.details_text)
        
        # Add panels to splitter
        results_splitter.addWidget(left_panel)
        results_splitter.addWidget(right_panel)
        results_splitter.setSizes([700, 700])  # Equal sizes
        
        results_layout.addWidget(results_splitter)
        
        self.tab_widget.addTab(results_widget, "📋 Search Results")
    
    def create_calendar_tab(self):
        """Create the release calendar tab"""
        calendar_widget = QWidget()
        calendar_layout = QHBoxLayout(calendar_widget)
        calendar_layout.setContentsMargins(4, 4, 4, 4)
        
        # Create splitter
        calendar_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left panel - Calendar
        calendar_panel = QWidget()
        calendar_panel_layout = QVBoxLayout(calendar_panel)
        calendar_panel_layout.setContentsMargins(4, 4, 4, 4)
        
        calendar_title = QLabel("🗓️ Release Date Calendar")
        calendar_title.setStyleSheet("color: #0078d4; font-weight: bold; font-size: 14px; margin: 4px;")
        calendar_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        calendar_panel_layout.addWidget(calendar_title)
        
        # Calendar widget
        self.calendar = QCalendarWidget()
        self.calendar.setMinimumSize(350, 250)  # Smaller calendar
        self.calendar.clicked.connect(self.calendar_date_clicked)
        calendar_panel_layout.addWidget(self.calendar)
        
        # Right panel - Date details
        date_panel = QWidget()
        date_panel_layout = QVBoxLayout(date_panel)
        date_panel_layout.setContentsMargins(4, 4, 4, 4)
        
        date_title = QLabel("📅 Found Release Dates")
        date_title.setStyleSheet("color: #0078d4; font-weight: bold; font-size: 14px; margin: 4px;")
        date_panel_layout.addWidget(date_title)
        
        # Dates list
        self.dates_list = QListWidget()
        date_panel_layout.addWidget(self.dates_list)
        
        # Selected date info
        selected_date_title = QLabel("📝 Selected Date Info")
        selected_date_title.setStyleSheet("color: #cccccc; font-weight: bold; font-size: 12px; margin: 4px;")
        date_panel_layout.addWidget(selected_date_title)
        
        self.selected_date_text = QTextEdit()
        self.selected_date_text.setPlaceholderText("Click on a date in the calendar or select from the dates list...")
        self.selected_date_text.setMaximumHeight(120)  # Smaller text area
        date_panel_layout.addWidget(self.selected_date_text)
        
        # Add panels to splitter
        calendar_splitter.addWidget(calendar_panel)
        calendar_splitter.addWidget(date_panel)
        calendar_splitter.setSizes([600, 800])
        
        calendar_layout.addWidget(calendar_splitter)
        
        self.tab_widget.addTab(calendar_widget, "🗓️ Release Calendar")
    
    def create_table_tab(self):
        """Create the detailed results table tab"""
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)
        table_layout.setContentsMargins(4, 4, 4, 4)
        
        table_title = QLabel("📊 Detailed Results Table")
        table_title.setStyleSheet("color: #0078d4; font-weight: bold; font-size: 14px; margin: 4px;")
        table_layout.addWidget(table_title)
        
        # Results table
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(4)
        self.results_table.setHorizontalHeaderLabels(["Source", "Title", "URL", "Description"])
        
        # Set column widths
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # Source
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Title
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # URL
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)  # Description
        
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        table_layout.addWidget(self.results_table)
        
        self.tab_widget.addTab(table_widget, "📊 Results Table")
    
    def start_search(self):
        """Start the anime search"""
        anime_name = self.anime_input.text().strip()
        if not anime_name:
            QMessageBox.warning(self, "Input Error", "Please enter an anime name!")
            return
        
        # Clear previous results
        self.results = []
        self.dates = []
        self.results_list.clear()
        self.dates_list.clear()
        self.details_text.clear()
        self.selected_date_text.clear()
        self.results_table.setRowCount(0)
        
        # Clear calendar highlights
        self.calendar.setDateTextFormat(QDate(), QTextCharFormat())
        
        # Update UI
        self.search_button.setEnabled(False)
        self.search_button.setText("🔍 Searching...")
        self.progress_bar.setValue(0)
        self.results_count_label.setText("Starting search...")
        
        # Start search worker
        use_tor = self.tor_checkbox.isChecked()
        self.search_worker = SearchWorker(anime_name, use_tor)
        self.search_worker.progress_updated.connect(self.update_progress)
        self.search_worker.result_found.connect(self.add_result)
        self.search_worker.search_complete.connect(self.search_finished)
        self.search_worker.start()
    
    def update_progress(self, value, message):
        """Update progress bar and status"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
    
    def add_result(self, result):
        """Add a new result to the list and table"""
        self.results.append(result)
        
        # Add to results list
        item_text = f"[{result['source']}] {result['title'][:100]}..."
        item = QListWidgetItem(item_text)
        item.setData(Qt.ItemDataRole.UserRole, len(self.results) - 1)
        self.results_list.addItem(item)
        
        # Add to results table
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)
        
        self.results_table.setItem(row, 0, QTableWidgetItem(result['source']))
        self.results_table.setItem(row, 1, QTableWidgetItem(result['title']))
        self.results_table.setItem(row, 2, QTableWidgetItem(result['url']))
        self.results_table.setItem(row, 3, QTableWidgetItem(result['snippet'][:200] + "..."))
        
        # Update count
        self.results_count_label.setText(f"Found {len(self.results)} results")
    
    def search_finished(self, all_results, dates):
        """Handle search completion"""
        self.results = all_results
        self.dates = dates
        
        # Update dates list
        self.dates_list.clear()
        for date_str in sorted(set(dates)):
            self.dates_list.addItem(f"📅 {date_str}")
        
        # Highlight dates on calendar
        self.highlight_calendar_dates(dates)
        
        # Update UI
        self.search_button.setEnabled(True)
        self.search_button.setText("🚀 Start Comprehensive Search")
        self.progress_bar.setValue(100)
        self.status_label.setText(f"✅ Search completed! Found {len(all_results)} results")
        
        if not all_results:
            self.results_count_label.setText("❌ No results found")
            self.details_text.setText("No results found. Try:\n• Different anime name spelling\n• Check internet connection\n• Enable Tor search for more sources")
        else:
            self.results_count_label.setText(f"✅ Found {len(all_results)} results")
    
    def highlight_calendar_dates(self, dates):
        """Highlight found dates on the calendar"""
        highlight_format = QTextCharFormat()
        highlight_format.setBackground(Qt.GlobalColor.blue)
        highlight_format.setForeground(Qt.GlobalColor.white)
        
        for date_str in dates:
            try:
                # Try to parse the date string
                for fmt in ['%Y-%m-%d', '%m/%d/%Y', '%B %d, %Y', '%d %B %Y']:
                    try:
                        parsed_date = datetime.strptime(date_str, fmt).date()
                        qdate = QDate(parsed_date.year, parsed_date.month, parsed_date.day)
                        self.calendar.setDateTextFormat(qdate, highlight_format)
                        break
                    except ValueError:
                        continue
            except:
                continue
    
    def calendar_date_clicked(self, qdate):
        """Handle calendar date click"""
        selected_date = qdate.toPython()
        date_info = f"Selected Date: {selected_date.strftime('%B %d, %Y')}\n\n"
        
        # Find results related to this date
        related_results = []
        for result in self.results:
            if selected_date.strftime('%Y-%m-%d') in result['snippet'] or \
               selected_date.strftime('%B %d, %Y') in result['snippet']:
                related_results.append(result)
        
        if related_results:
            date_info += f"Found {len(related_results)} results related to this date:\n\n"
            for i, result in enumerate(related_results[:3], 1):
                date_info += f"{i}. [{result['source']}] {result['title'][:50]}...\n"
        else:
            date_info += "No specific results found for this date."
        
        self.selected_date_text.setText(date_info)
    
    def show_result_details(self, item):
        """Show details for selected result"""
        index = item.data(Qt.ItemDataRole.UserRole)
        if index is not None and 0 <= index < len(self.results):
            result = self.results[index]
            
            details = f"""🔍 SOURCE: {result['source']}

📰 TITLE: {result['title']}

🔗 URL: {result['url']}

📝 DESCRIPTION:
{result['snippet']}

---
💡 TIP: Copy the URL to visit the source page for more information."""
            
            self.details_text.setText(details.strip())

def main():
    """Main function to run the GUI"""
    app = QApplication(sys.argv)
    app.setApplicationName("Anime Release Finder")
    
    # Create and show main window
    window = AnimeSearchGUI()
    window.show()
    
    # Start event loop
    sys.exit(app.exec())

if __name__ == "__main__":
    main()